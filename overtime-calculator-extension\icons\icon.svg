<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="clockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffed4e;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="4"/>
  
  <!-- 时钟外圈 -->
  <circle cx="64" cy="64" r="45" fill="none" stroke="url(#clockGradient)" stroke-width="3"/>
  
  <!-- 时钟刻度 -->
  <g stroke="url(#clockGradient)" stroke-width="2" fill="none">
    <!-- 12点 -->
    <line x1="64" y1="24" x2="64" y2="30"/>
    <!-- 3点 -->
    <line x1="104" y1="64" x2="98" y2="64"/>
    <!-- 6点 -->
    <line x1="64" y1="104" x2="64" y2="98"/>
    <!-- 9点 -->
    <line x1="24" y1="64" x2="30" y2="64"/>
  </g>
  
  <!-- 时针指向7点（19:00） -->
  <line x1="64" y1="64" x2="50" y2="85" stroke="#ffffff" stroke-width="4" stroke-linecap="round"/>
  
  <!-- 分针指向12点 -->
  <line x1="64" y1="64" x2="64" y2="35" stroke="#ffffff" stroke-width="3" stroke-linecap="round"/>
  
  <!-- 中心点 -->
  <circle cx="64" cy="64" r="4" fill="#ffffff"/>
  
  <!-- 加班标识 "OT" -->
  <text x="64" y="115" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#ffffff">OT</text>
  
  <!-- 装饰性元素 -->
  <circle cx="85" cy="35" r="3" fill="#ffd700" opacity="0.8"/>
  <circle cx="95" cy="45" r="2" fill="#ffffff" opacity="0.6"/>
  <circle cx="35" cy="85" r="2.5" fill="#ffd700" opacity="0.7"/>
</svg>
