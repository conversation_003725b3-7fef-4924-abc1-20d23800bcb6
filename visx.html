<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VSIX URL Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f4f4f4;
        }
        h1 {
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        input {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        button {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #005a8c;
        }
        #download-link {
            margin-top: 10px;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>VSIX URL Generator</h1>
    <p>Enter the plugin's Identifier and Version to generate the VSIX download URL:</p>
    <label for="identifier">Identifier (e.g., ms-vscode.vscode-speech-language-pack-zh-cn):</label>
    <input type="text" id="identifier" placeholder="e.g., ms-vscode.vscode-speech-language-pack-zh-cn" />
    <label for="version">Version (e.g., 0.5.1):</label>
    <input type="text" id="version" placeholder="e.g., 0.5.1" />
    <button onclick="generateVsixUrl()">Generate VSIX URL</button>
    <p id="download-link"></p>
</div>

<script>
    function generateVsixUrl() {
        // Get the values from the input fields
        const identifier = document.getElementById('identifier').value.trim();
        const version = document.getElementById('version').value.trim();

        if (identifier && version) {
            // Extract publisher and name from the identifier
            const [publisher, name] = identifier.split('.');

            // Generate the VSIX URL
            const vsixUrl = `https://marketplace.visualstudio.com/_apis/public/gallery/publishers/${publisher}/vsextensions/${name}/${version}/vspackage`;

            // Display the generated URL
            document.getElementById('download-link').innerHTML = `Click to download the VSIX file: <a href="${vsixUrl}" target="_blank">${vsixUrl}</a>`;
        } else {
            document.getElementById('download-link').innerHTML = "Please enter both the Identifier and Version.";
        }
    }
</script>

</body>
</html>
