@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    加班时间计算器 Chrome 扩展安装器
echo ========================================
echo.

echo 🚀 正在准备安装加班时间计算器插件...
echo.

echo 📋 安装步骤：
echo 1. 打开 Chrome 浏览器
echo 2. 在地址栏输入：chrome://extensions/
echo 3. 开启右上角的"开发者模式"开关
echo 4. 点击"加载已解压的扩展程序"
echo 5. 选择当前文件夹：%~dp0
echo 6. 点击"选择文件夹"完成安装
echo.

echo 💡 使用提示：
echo - 安装后插件图标会出现在浏览器工具栏
echo - 在包含打卡表格的页面点击插件图标使用
echo - 支持快捷键：Ctrl+Shift+O 计算，Ctrl+Shift+C 清除
echo.

echo ⚠️  注意事项：
echo - 需要 Chrome 88+ 版本
echo - 首次使用需要生成图标文件
echo - 如遇问题请查看 README.md 文档
echo.

set /p choice="是否现在打开 Chrome 扩展管理页面？(Y/N): "
if /i "%choice%"=="Y" (
    echo.
    echo 🌐 正在打开 Chrome 扩展管理页面...
    start chrome://extensions/
    echo.
    echo ✅ 请按照上述步骤完成安装
) else (
    echo.
    echo 📝 请手动打开 chrome://extensions/ 完成安装
)

echo.
echo 🎯 当前插件文件夹路径：
echo %~dp0
echo.

set /p choice2="是否需要生成图标文件？(Y/N): "
if /i "%choice2%"=="Y" (
    echo.
    echo 🎨 正在打开图标生成器...
    start generate-icons.html
    echo.
    echo 📌 请在打开的页面中：
    echo 1. 点击"生成所有图标"按钮
    echo 2. 依次下载所有尺寸的图标
    echo 3. 将图标文件保存到 icons 文件夹中
)

echo.
echo 🎉 安装准备完成！
echo 📖 详细说明请查看 README.md 文件
echo.
pause
