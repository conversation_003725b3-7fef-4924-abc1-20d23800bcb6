<table data-type="table" class="table table-bordered">
    <thead>
        <tr>
            <th>序号</th>
            <th>打卡日期</th>
            <th>星期</th>
            <th>第一次</th>
            <th>第二次</th>
            <th>第三次</th>
            <th>第四次</th>
            <th>第五次</th>
            <th>第六次</th>
        </tr>
    </thead>
    <tbody><!---->
        <tr class="footable-even" style="display: table-row;">
            <td>1</td>
            <td>2025-08-19</td>
            <td>二</td>
            <td>08:17:41</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>2</td>
            <td>2025-08-18</td>
            <td>一</td>
            <td>08:13:45</td>
            <td>20:35:54</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>3</td>
            <td>2025-08-17</td>
            <td>日</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>4</td>
            <td>2025-08-16</td>
            <td>六</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>5</td>
            <td>2025-08-15</td>
            <td>五</td>
            <td>08:01:51</td>
            <td>18:02:36</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>6</td>
            <td>2025-08-14</td>
            <td>四</td>
            <td>07:57:30</td>
            <td>21:42:49</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>7</td>
            <td>2025-08-13</td>
            <td>三</td>
            <td>07:50:50</td>
            <td>07:55:36</td>
            <td>20:00:22</td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>8</td>
            <td>2025-08-12</td>
            <td>二</td>
            <td>07:45:55</td>
            <td>21:52:16</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>9</td>
            <td>2025-08-11</td>
            <td>一</td>
            <td>08:02:24</td>
            <td>20:12:11</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>10</td>
            <td>2025-08-10</td>
            <td>日</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>11</td>
            <td>2025-08-09</td>
            <td>六</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>12</td>
            <td>2025-08-08</td>
            <td>五</td>
            <td>07:56:30</td>
            <td>18:02:34</td>
            <td>18:03:33</td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>13</td>
            <td>2025-08-07</td>
            <td>四</td>
            <td>07:49:10</td>
            <td>20:24:57</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>14</td>
            <td>2025-08-06</td>
            <td>三</td>
            <td>08:47:13</td>
            <td>22:07:27</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>15</td>
            <td>2025-08-05</td>
            <td>二</td>
            <td>21:57:40</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>16</td>
            <td>2025-08-04</td>
            <td>一</td>
            <td>08:02:17</td>
            <td>21:03:49</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>17</td>
            <td>2025-08-03</td>
            <td>日</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>18</td>
            <td>2025-08-02</td>
            <td>六</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr class="footable-even" style="display: table-row;">
            <td>19</td>
            <td>2025-08-01</td>
            <td>五</td>
            <td>07:51:10</td>
            <td>18:05:08</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>

<div style="margin-top: 20px;">
    <button onclick="calculateAndDisplayOvertime()" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
        计算加班时间
    </button>
    <div id="overtimeResults" style="margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 4px; display: none;">
        <h4>加班时间统计结果：</h4>
        <div id="overtimeDetails"></div>
        <div id="totalOvertime" style="font-weight: bold; margin-top: 10px; color: #dc3545;"></div>
    </div>
</div>

<script>
/**
 * 加班时间计算器
 * 根据打卡数据计算加班时长
 */

/**
 * 计算加班时间
 * @param {Array} attendanceData - 打卡数据数组
 * @returns {Array} 包含加班信息的数据数组
 */
function calculateOvertime(attendanceData) {
    const results = [];

    attendanceData.forEach(record => {
        const overtimeInfo = calculateDailyOvertime(record);
        results.push({
            ...record,
            overtimeHours: overtimeInfo.hours,
            overtimeMinutes: overtimeInfo.minutes,
            overtimeReason: overtimeInfo.reason
        });
    });

    return results;
}

/**
 * 计算单日加班时间
 * @param {Object} record - 单日打卡记录
 * @returns {Object} 加班信息 {hours, minutes, reason}
 */
function calculateDailyOvertime(record) {
    const { date, dayOfWeek, punchTimes } = record;

    // 过滤掉空的打卡时间
    const validPunchTimes = punchTimes.filter(time => time && time.trim() !== '');

    // 如果没有打卡记录，返回0
    if (validPunchTimes.length === 0) {
        return { hours: 0, minutes: 0, reason: '无打卡记录' };
    }

    // 判断是否为周末
    const isWeekend = dayOfWeek === '六' || dayOfWeek === '日';

    if (isWeekend) {
        return calculateWeekendOvertime(validPunchTimes);
    } else {
        return calculateWeekdayOvertime(validPunchTimes);
    }
}

/**
 * 计算工作日加班时间
 * @param {Array} punchTimes - 打卡时间数组
 * @returns {Object} 加班信息
 */
function calculateWeekdayOvertime(punchTimes) {
    // 如果第一次打卡时间大于12点，不统计加班
    const firstPunch = parseTime(punchTimes[0]);
    if (firstPunch.hours >= 12) {
        return { hours: 0, minutes: 0, reason: '第一次打卡时间大于12点' };
    }

    // 如果只有一次打卡且在19点前，不统计加班
    if (punchTimes.length === 1) {
        if (firstPunch.hours < 19) {
            return { hours: 0, minutes: 0, reason: '无下班卡且19点前无打卡记录' };
        }
    }

    // 找到最后一次打卡时间
    const lastPunch = parseTime(punchTimes[punchTimes.length - 1]);

    // 如果最后一次打卡在19点前，不统计加班
    if (lastPunch.hours < 19) {
        return { hours: 0, minutes: 0, reason: '下班卡未超过19点' };
    }

    // 计算标准下班时间（考虑弹性打卡）
    const standardEndTime = calculateStandardEndTime(firstPunch);

    // 计算加班时间（从19点开始算加班）
    const overtimeStartTime = { hours: 19, minutes: 0 };
    const overtimeMinutes = calculateTimeDifference(overtimeStartTime, lastPunch);

    // 减去早上弹性时间（如果晚到了）
    const flexMinutes = calculateFlexTime(firstPunch);
    const finalOvertimeMinutes = Math.max(0, overtimeMinutes - flexMinutes);

    return {
        hours: Math.floor(finalOvertimeMinutes / 60),
        minutes: finalOvertimeMinutes % 60,
        reason: finalOvertimeMinutes > 0 ? '工作日加班' : '无加班'
    };
}

/**
 * 计算周末加班时间
 * @param {Array} punchTimes - 打卡时间数组
 * @returns {Object} 加班信息
 */
function calculateWeekendOvertime(punchTimes) {
    if (punchTimes.length < 2) {
        return { hours: 0, minutes: 0, reason: '周末打卡记录不完整' };
    }

    // 按时间排序
    const sortedTimes = punchTimes.map(parseTime).sort((a, b) => {
        return (a.hours * 60 + a.minutes) - (b.hours * 60 + b.minutes);
    });

    const firstPunch = sortedTimes[0];
    const lastPunch = sortedTimes[sortedTimes.length - 1];

    // 计算总工作时间
    let totalMinutes = calculateTimeDifference(firstPunch, lastPunch);

    // 如果跨越午休时间（12:00-13:00），减去午休时间
    if (firstPunch.hours < 12 && lastPunch.hours >= 13) {
        totalMinutes -= 60; // 减去1小时午休
    } else if (firstPunch.hours < 13 && lastPunch.hours >= 13 &&
               (firstPunch.hours > 12 || (firstPunch.hours === 12 && firstPunch.minutes > 0))) {
        // 如果开始时间在12点后但13点前，只减去部分午休时间
        const lunchStartMinutes = Math.max(0, 60 - firstPunch.minutes);
        totalMinutes -= lunchStartMinutes;
    }

    return {
        hours: Math.floor(totalMinutes / 60),
        minutes: totalMinutes % 60,
        reason: totalMinutes > 0 ? '周末加班' : '无加班'
    };
}

/**
 * 解析时间字符串
 * @param {string} timeStr - 时间字符串 "HH:MM:SS"
 * @returns {Object} {hours, minutes, seconds}
 */
function parseTime(timeStr) {
    const parts = timeStr.split(':');
    return {
        hours: parseInt(parts[0], 10),
        minutes: parseInt(parts[1], 10),
        seconds: parseInt(parts[2], 10)
    };
}

/**
 * 计算标准下班时间（考虑弹性打卡）
 * @param {Object} firstPunch - 第一次打卡时间
 * @returns {Object} 标准下班时间
 */
function calculateStandardEndTime(firstPunch) {
    const standardStartHour = 8;
    const standardStartMinute = 30;
    const standardEndHour = 18;
    const standardEndMinute = 0;

    // 如果在标准时间前打卡，下班时间为18:00
    if (firstPunch.hours < standardStartHour ||
        (firstPunch.hours === standardStartHour && firstPunch.minutes <= standardStartMinute)) {
        return { hours: standardEndHour, minutes: standardEndMinute };
    }

    // 如果晚到，下班时间相应延后
    const lateMinutes = (firstPunch.hours - standardStartHour) * 60 +
                       (firstPunch.minutes - standardStartMinute);
    const endTotalMinutes = standardEndHour * 60 + standardEndMinute + lateMinutes;

    return {
        hours: Math.floor(endTotalMinutes / 60),
        minutes: endTotalMinutes % 60
    };
}

/**
 * 计算弹性时间（早上晚到的时间）
 * @param {Object} firstPunch - 第一次打卡时间
 * @returns {number} 弹性时间（分钟）
 */
function calculateFlexTime(firstPunch) {
    const standardStartHour = 8;
    const standardStartMinute = 30;

    if (firstPunch.hours < standardStartHour ||
        (firstPunch.hours === standardStartHour && firstPunch.minutes <= standardStartMinute)) {
        return 0;
    }

    return (firstPunch.hours - standardStartHour) * 60 +
           (firstPunch.minutes - standardStartMinute);
}

/**
 * 计算两个时间之间的分钟差
 * @param {Object} startTime - 开始时间
 * @param {Object} endTime - 结束时间
 * @returns {number} 分钟差
 */
function calculateTimeDifference(startTime, endTime) {
    const startMinutes = startTime.hours * 60 + startTime.minutes;
    const endMinutes = endTime.hours * 60 + endTime.minutes;
    return Math.max(0, endMinutes - startMinutes);
}

/**
 * 从HTML表格中提取打卡数据
 * @returns {Array} 打卡数据数组
 */
function extractAttendanceDataFromTable() {
    const table = document.querySelector('table');
    if (!table) {
        throw new Error('找不到表格');
    }

    const rows = table.querySelectorAll('tbody tr');
    const data = [];

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 8) {
            const punchTimes = [];
            for (let i = 3; i <= 8; i++) {
                const timeText = cells[i].textContent.trim();
                if (timeText) {
                    punchTimes.push(timeText);
                }
            }

            data.push({
                序号: cells[0].textContent.trim(),
                date: cells[1].textContent.trim(),
                dayOfWeek: cells[2].textContent.trim(),
                punchTimes: punchTimes
            });
        }
    });

    return data;
}

/**
 * 主函数：计算并显示加班数据
 */
function calculateAndDisplayOvertime() {
    try {
        // 从表格中提取数据
        const attendanceData = extractAttendanceDataFromTable();

        // 计算加班时间
        const overtimeResults = calculateOvertime(attendanceData);

        // 显示结果到页面
        displayOvertimeResults(overtimeResults);

        return overtimeResults;
    } catch (error) {
        console.error('计算加班时间时出错：', error);
        alert('计算加班时间时出错：' + error.message);
        return [];
    }
}

/**
 * 在页面上显示加班结果
 * @param {Array} overtimeResults - 加班计算结果
 */
function displayOvertimeResults(overtimeResults) {
    const resultsDiv = document.getElementById('overtimeResults');
    const detailsDiv = document.getElementById('overtimeDetails');
    const totalDiv = document.getElementById('totalOvertime');

    // 清空之前的结果
    detailsDiv.innerHTML = '';

    // 显示每日加班详情
    let hasOvertime = false;
    overtimeResults.forEach(record => {
        if (record.overtimeHours > 0 || record.overtimeMinutes > 0) {
            hasOvertime = true;
            const div = document.createElement('div');
            div.style.marginBottom = '5px';
            div.innerHTML = `<strong>${record.date} (${record.dayOfWeek}):</strong> ${record.overtimeHours}小时${record.overtimeMinutes}分钟 <span style="color: #6c757d;">- ${record.overtimeReason}</span>`;
            detailsDiv.appendChild(div);
        }
    });

    if (!hasOvertime) {
        detailsDiv.innerHTML = '<div style="color: #6c757d;">本期间无加班记录</div>';
    }

    // 计算总加班时间
    const totalOvertimeMinutes = overtimeResults.reduce((total, record) => {
        return total + record.overtimeHours * 60 + record.overtimeMinutes;
    }, 0);

    const totalHours = Math.floor(totalOvertimeMinutes / 60);
    const totalMinutes = totalOvertimeMinutes % 60;

    totalDiv.innerHTML = `总加班时间: ${totalHours}小时${totalMinutes}分钟`;

    // 显示结果区域
    resultsDiv.style.display = 'block';
}
</script>