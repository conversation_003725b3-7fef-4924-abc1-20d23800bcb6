/* 加班计算器插件样式文件 */

/* 插件结果容器的基础样式 */
#overtime-extension-result {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 400px !important;
    max-width: calc(100vw - 40px) !important;
    max-height: 500px !important;
    overflow-y: auto !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
    color: white !important;
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
    z-index: 2147483647 !important; /* 最高层级 */
    padding: 20px !important;
    box-sizing: border-box !important;
    animation: slideInFromRight 0.3s ease-out !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    #overtime-extension-result {
        width: calc(100vw - 20px) !important;
        right: 10px !important;
        bottom: 10px !important;
        max-height: 60vh !important;
    }
}

/* 滑入动画 */
@keyframes slideInFromRight {
    from {
        transform: translateX(100%) !important;
        opacity: 0 !important;
    }
    to {
        transform: translateX(0) !important;
        opacity: 1 !important;
    }
}

/* 滚动条样式 */
#overtime-extension-result::-webkit-scrollbar {
    width: 6px !important;
}

#overtime-extension-result::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1) !important;
    border-radius: 3px !important;
}

#overtime-extension-result::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3) !important;
    border-radius: 3px !important;
}

#overtime-extension-result::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5) !important;
}

/* 关闭按钮样式 */
#overtime-extension-result .close-btn {
    background: rgba(255,255,255,0.2) !important;
    border: none !important;
    color: white !important;
    border-radius: 50% !important;
    width: 24px !important;
    height: 24px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: background-color 0.2s ease !important;
}

#overtime-extension-result .close-btn:hover {
    background: rgba(255,255,255,0.3) !important;
}

/* 详情项样式 */
#overtime-extension-result .overtime-detail-item {
    margin-bottom: 8px !important;
    padding: 8px !important;
    background: rgba(255,255,255,0.15) !important;
    border-radius: 6px !important;
    border-left: 4px solid #ffd700 !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
}

/* 总计样式 */
#overtime-extension-result .total-overtime {
    text-align: center !important;
    font-size: 16px !important;
    font-weight: bold !important;
    padding: 12px !important;
    background: rgba(255,255,255,0.2) !important;
    border-radius: 8px !important;
    margin-top: 15px !important;
}

/* 标题样式 */
#overtime-extension-result .title {
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
}

/* 版权信息样式 */
#overtime-extension-result .footer {
    text-align: center !important;
    margin-top: 10px !important;
    font-size: 11px !important;
    opacity: 0.7 !important;
}

/* 无加班记录样式 */
#overtime-extension-result .no-overtime {
    text-align: center !important;
    padding: 20px !important;
    opacity: 0.8 !important;
    font-style: italic !important;
}

/* 确保插件样式不被页面样式覆盖 */
#overtime-extension-result * {
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
}

#overtime-extension-result h3,
#overtime-extension-result div,
#overtime-extension-result span,
#overtime-extension-result button {
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
    color: inherit !important;
}

/* 防止页面样式干扰 */
#overtime-extension-result {
    all: initial !important;
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 400px !important;
    max-width: calc(100vw - 40px) !important;
    max-height: 500px !important;
    overflow-y: auto !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
    color: white !important;
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
    z-index: 2147483647 !important;
    padding: 20px !important;
    box-sizing: border-box !important;
    animation: slideInFromRight 0.3s ease-out !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}
