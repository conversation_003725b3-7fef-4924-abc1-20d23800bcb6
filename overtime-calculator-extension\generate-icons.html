<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            align-items: center;
        }
        .icon-size {
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>加班计算器插件图标生成器</h1>
        <p>点击下面的按钮生成不同尺寸的图标文件</p>
        
        <div class="icon-preview">
            <div class="icon-size">
                <h3>16x16</h3>
                <canvas id="canvas16" width="16" height="16"></canvas>
                <br>
                <button onclick="downloadIcon(16)">下载 icon16.png</button>
            </div>
            
            <div class="icon-size">
                <h3>32x32</h3>
                <canvas id="canvas32" width="32" height="32"></canvas>
                <br>
                <button onclick="downloadIcon(32)">下载 icon32.png</button>
            </div>
            
            <div class="icon-size">
                <h3>48x48</h3>
                <canvas id="canvas48" width="48" height="48"></canvas>
                <br>
                <button onclick="downloadIcon(48)">下载 icon48.png</button>
            </div>
            
            <div class="icon-size">
                <h3>128x128</h3>
                <canvas id="canvas128" width="128" height="128"></canvas>
                <br>
                <button onclick="downloadIcon(128)">下载 icon128.png</button>
            </div>
        </div>
        
        <button onclick="generateAllIcons()" style="background: #28a745; font-size: 16px; padding: 15px 30px;">
            🚀 生成所有图标
        </button>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"生成所有图标"按钮</li>
                <li>依次下载所有尺寸的图标文件</li>
                <li>将下载的图标文件放入 <code>icons/</code> 文件夹中</li>
                <li>确保文件名为：icon16.png, icon32.png, icon48.png, icon128.png</li>
            </ol>
        </div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.4;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制背景圆形
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fillStyle = gradient;
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = size > 32 ? 2 : 1;
            ctx.stroke();
            
            // 绘制时钟外圈
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius * 0.8, 0, 2 * Math.PI);
            ctx.strokeStyle = '#ffd700';
            ctx.lineWidth = size > 32 ? 2 : 1;
            ctx.stroke();
            
            // 绘制时钟刻度（仅在较大尺寸时显示）
            if (size >= 32) {
                ctx.strokeStyle = '#ffd700';
                ctx.lineWidth = 1;
                
                // 12点刻度
                ctx.beginPath();
                ctx.moveTo(centerX, centerY - radius * 0.8);
                ctx.lineTo(centerX, centerY - radius * 0.7);
                ctx.stroke();
                
                // 6点刻度
                ctx.beginPath();
                ctx.moveTo(centerX, centerY + radius * 0.8);
                ctx.lineTo(centerX, centerY + radius * 0.7);
                ctx.stroke();
                
                // 3点刻度
                ctx.beginPath();
                ctx.moveTo(centerX + radius * 0.8, centerY);
                ctx.lineTo(centerX + radius * 0.7, centerY);
                ctx.stroke();
                
                // 9点刻度
                ctx.beginPath();
                ctx.moveTo(centerX - radius * 0.8, centerY);
                ctx.lineTo(centerX - radius * 0.7, centerY);
                ctx.stroke();
            }
            
            // 绘制时针（指向7点，表示19:00）
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.lineTo(centerX - radius * 0.3, centerY + radius * 0.4);
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = size > 32 ? 3 : 2;
            ctx.lineCap = 'round';
            ctx.stroke();
            
            // 绘制分针（指向12点）
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.lineTo(centerX, centerY - radius * 0.6);
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = size > 32 ? 2 : 1;
            ctx.lineCap = 'round';
            ctx.stroke();
            
            // 绘制中心点
            ctx.beginPath();
            ctx.arc(centerX, centerY, size > 32 ? 3 : 2, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            
            // 绘制"OT"文字（仅在较大尺寸时显示）
            if (size >= 48) {
                ctx.fillStyle = '#ffffff';
                ctx.font = `bold ${size * 0.12}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('OT', centerX, centerY + radius * 1.3);
            }
            
            // 添加装饰点（仅在较大尺寸时显示）
            if (size >= 48) {
                // 右上角装饰点
                ctx.beginPath();
                ctx.arc(centerX + radius * 0.6, centerY - radius * 0.6, 2, 0, 2 * Math.PI);
                ctx.fillStyle = '#ffd700';
                ctx.globalAlpha = 0.8;
                ctx.fill();
                ctx.globalAlpha = 1;
                
                // 左下角装饰点
                ctx.beginPath();
                ctx.arc(centerX - radius * 0.6, centerY + radius * 0.6, 1.5, 0, 2 * Math.PI);
                ctx.fillStyle = '#ffffff';
                ctx.globalAlpha = 0.6;
                ctx.fill();
                ctx.globalAlpha = 1;
            }
        }
        
        function generateAllIcons() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                drawIcon(canvas, size);
            });
            alert('所有图标已生成！请依次点击下载按钮保存图标文件。');
        }
        
        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 页面加载时自动生成图标
        window.onload = function() {
            generateAllIcons();
        };
    </script>
</body>
</html>
