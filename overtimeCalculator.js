/**
 * 加班时间计算器
 * 根据打卡数据计算加班时长
 */

/**
 * 计算加班时间
 * @param {Array} attendanceData - 打卡数据数组
 * @returns {Array} 包含加班信息的数据数组
 */
function calculateOvertime(attendanceData) {
    const results = [];
    
    attendanceData.forEach(record => {
        const overtimeInfo = calculateDailyOvertime(record);
        results.push({
            ...record,
            overtimeHours: overtimeInfo.hours,
            overtimeMinutes: overtimeInfo.minutes,
            overtimeReason: overtimeInfo.reason
        });
    });
    
    return results;
}

/**
 * 计算单日加班时间
 * @param {Object} record - 单日打卡记录
 * @returns {Object} 加班信息 {hours, minutes, reason}
 */
function calculateDailyOvertime(record) {
    const { date, dayOfWeek, punchTimes } = record;
    
    // 过滤掉空的打卡时间
    const validPunchTimes = punchTimes.filter(time => time && time.trim() !== '');
    
    // 如果没有打卡记录，返回0
    if (validPunchTimes.length === 0) {
        return { hours: 0, minutes: 0, reason: '无打卡记录' };
    }
    
    // 判断是否为周末
    const isWeekend = dayOfWeek === '六' || dayOfWeek === '日';
    
    if (isWeekend) {
        return calculateWeekendOvertime(validPunchTimes);
    } else {
        return calculateWeekdayOvertime(validPunchTimes);
    }
}

/**
 * 计算工作日加班时间
 * @param {Array} punchTimes - 打卡时间数组
 * @returns {Object} 加班信息
 */
function calculateWeekdayOvertime(punchTimes) {
    // 如果第一次打卡时间大于12点，不统计加班
    const firstPunch = parseTime(punchTimes[0]);
    if (firstPunch.hours >= 12) {
        return { hours: 0, minutes: 0, reason: '第一次打卡时间大于12点' };
    }
    
    // 如果只有一次打卡且在18点前，不统计加班
    if (punchTimes.length === 1) {
        if (firstPunch.hours < 18) {
            return { hours: 0, minutes: 0, reason: '无下班卡且18点前无打卡记录' };
        }
    }
    
    // 找到最后一次打卡时间
    const lastPunch = parseTime(punchTimes[punchTimes.length - 1]);
    
    // 如果最后一次打卡在18点前，不统计加班
    if (lastPunch.hours < 18) {
        return { hours: 0, minutes: 0, reason: '18点前无打卡记录' };
    }
    
    // 计算标准下班时间（考虑弹性打卡）
    const standardEndTime = calculateStandardEndTime(firstPunch);
    
    // 计算加班时间
    const overtimeMinutes = calculateTimeDifference(standardEndTime, lastPunch);
    
    // 减去早上弹性时间（如果晚到了）
    const flexMinutes = calculateFlexTime(firstPunch);
    const finalOvertimeMinutes = Math.max(0, overtimeMinutes - flexMinutes);
    
    return {
        hours: Math.floor(finalOvertimeMinutes / 60),
        minutes: finalOvertimeMinutes % 60,
        reason: finalOvertimeMinutes > 0 ? '工作日加班' : '无加班'
    };
}

/**
 * 计算周末加班时间
 * @param {Array} punchTimes - 打卡时间数组
 * @returns {Object} 加班信息
 */
function calculateWeekendOvertime(punchTimes) {
    if (punchTimes.length < 2) {
        return { hours: 0, minutes: 0, reason: '周末打卡记录不完整' };
    }
    
    // 按时间排序
    const sortedTimes = punchTimes.map(parseTime).sort((a, b) => {
        return (a.hours * 60 + a.minutes) - (b.hours * 60 + b.minutes);
    });
    
    const firstPunch = sortedTimes[0];
    const lastPunch = sortedTimes[sortedTimes.length - 1];
    
    // 计算总工作时间
    let totalMinutes = calculateTimeDifference(firstPunch, lastPunch);
    
    // 如果跨越午休时间（12:00-13:00），减去午休时间
    if (firstPunch.hours < 12 && lastPunch.hours >= 13) {
        totalMinutes -= 60; // 减去1小时午休
    } else if (firstPunch.hours < 13 && lastPunch.hours >= 13 && 
               (firstPunch.hours > 12 || (firstPunch.hours === 12 && firstPunch.minutes > 0))) {
        // 如果开始时间在12点后但13点前，只减去部分午休时间
        const lunchStartMinutes = Math.max(0, 60 - firstPunch.minutes);
        totalMinutes -= lunchStartMinutes;
    }
    
    return {
        hours: Math.floor(totalMinutes / 60),
        minutes: totalMinutes % 60,
        reason: totalMinutes > 0 ? '周末加班' : '无加班'
    };
}

/**
 * 解析时间字符串
 * @param {string} timeStr - 时间字符串 "HH:MM:SS"
 * @returns {Object} {hours, minutes, seconds}
 */
function parseTime(timeStr) {
    const parts = timeStr.split(':');
    return {
        hours: parseInt(parts[0], 10),
        minutes: parseInt(parts[1], 10),
        seconds: parseInt(parts[2], 10)
    };
}

/**
 * 计算标准下班时间（考虑弹性打卡）
 * @param {Object} firstPunch - 第一次打卡时间
 * @returns {Object} 标准下班时间
 */
function calculateStandardEndTime(firstPunch) {
    const standardStartHour = 8;
    const standardStartMinute = 30;
    const standardEndHour = 18;
    const standardEndMinute = 0;
    
    // 如果在标准时间前打卡，下班时间为18:00
    if (firstPunch.hours < standardStartHour || 
        (firstPunch.hours === standardStartHour && firstPunch.minutes <= standardStartMinute)) {
        return { hours: standardEndHour, minutes: standardEndMinute };
    }
    
    // 如果晚到，下班时间相应延后
    const lateMinutes = (firstPunch.hours - standardStartHour) * 60 + 
                       (firstPunch.minutes - standardStartMinute);
    const endTotalMinutes = standardEndHour * 60 + standardEndMinute + lateMinutes;
    
    return {
        hours: Math.floor(endTotalMinutes / 60),
        minutes: endTotalMinutes % 60
    };
}

/**
 * 计算弹性时间（早上晚到的时间）
 * @param {Object} firstPunch - 第一次打卡时间
 * @returns {number} 弹性时间（分钟）
 */
function calculateFlexTime(firstPunch) {
    const standardStartHour = 8;
    const standardStartMinute = 30;
    
    if (firstPunch.hours < standardStartHour || 
        (firstPunch.hours === standardStartHour && firstPunch.minutes <= standardStartMinute)) {
        return 0;
    }
    
    return (firstPunch.hours - standardStartHour) * 60 + 
           (firstPunch.minutes - standardStartMinute);
}

/**
 * 计算两个时间之间的分钟差
 * @param {Object} startTime - 开始时间
 * @param {Object} endTime - 结束时间
 * @returns {number} 分钟差
 */
function calculateTimeDifference(startTime, endTime) {
    const startMinutes = startTime.hours * 60 + startTime.minutes;
    const endMinutes = endTime.hours * 60 + endTime.minutes;
    return Math.max(0, endMinutes - startMinutes);
}

/**
 * 从HTML表格中提取打卡数据
 * @param {string} tableSelector - 表格选择器
 * @returns {Array} 打卡数据数组
 */
function extractAttendanceDataFromTable(tableSelector = 'table') {
    const table = document.querySelector(tableSelector);
    if (!table) {
        throw new Error('找不到表格');
    }
    
    const rows = table.querySelectorAll('tbody tr');
    const data = [];
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 8) {
            const punchTimes = [];
            for (let i = 3; i <= 8; i++) {
                const timeText = cells[i].textContent.trim();
                if (timeText) {
                    punchTimes.push(timeText);
                }
            }
            
            data.push({
                序号: cells[0].textContent.trim(),
                date: cells[1].textContent.trim(),
                dayOfWeek: cells[2].textContent.trim(),
                punchTimes: punchTimes
            });
        }
    });
    
    return data;
}

/**
 * 主函数：计算并显示加班数据
 */
function calculateAndDisplayOvertime() {
    try {
        // 从表格中提取数据
        const attendanceData = extractAttendanceDataFromTable();
        
        // 计算加班时间
        const overtimeResults = calculateOvertime(attendanceData);
        
        // 显示结果
        console.log('加班时间计算结果：');
        overtimeResults.forEach(record => {
            if (record.overtimeHours > 0 || record.overtimeMinutes > 0) {
                console.log(`${record.date} (${record.dayOfWeek}): ${record.overtimeHours}小时${record.overtimeMinutes}分钟 - ${record.overtimeReason}`);
            }
        });
        
        // 计算总加班时间
        const totalOvertimeMinutes = overtimeResults.reduce((total, record) => {
            return total + record.overtimeHours * 60 + record.overtimeMinutes;
        }, 0);
        
        const totalHours = Math.floor(totalOvertimeMinutes / 60);
        const totalMinutes = totalOvertimeMinutes % 60;
        
        console.log(`\n总加班时间: ${totalHours}小时${totalMinutes}分钟`);
        
        return overtimeResults;
    } catch (error) {
        console.error('计算加班时间时出错：', error);
        return [];
    }
}

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        calculateOvertime,
        calculateDailyOvertime,
        extractAttendanceDataFromTable,
        calculateAndDisplayOvertime
    };
}
