// Content script for overtime calculator extension
// This script runs on all pages and provides additional functionality

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'calculateOvertime') {
        try {
            const result = calculateOvertimeInCurrentPage();
            sendResponse({ success: true, data: result });
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    } else if (request.action === 'clearResults') {
        clearOvertimeResults();
        sendResponse({ success: true });
    }
    return true; // 保持消息通道开放
});

// 添加快捷键支持
document.addEventListener('keydown', function(event) {
    // Ctrl+Shift+O 快速计算加班时间
    if (event.ctrlKey && event.shiftKey && event.key === 'O') {
        event.preventDefault();
        try {
            calculateOvertimeInCurrentPage();
            console.log('🚀 快捷键触发：加班时间计算完成');
        } catch (error) {
            console.error('❌ 快捷键计算失败:', error);
        }
    }
    
    // Ctrl+Shift+C 清除结果
    if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        clearOvertimeResults();
        console.log('🗑️ 快捷键触发：结果已清除');
    }
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查页面是否包含打卡表格
    const tables = document.querySelectorAll('table');
    let hasAttendanceTable = false;
    
    for (let table of tables) {
        const headers = table.querySelectorAll('th');
        const headerTexts = Array.from(headers).map(th => th.textContent.trim());
        
        if (headerTexts.some(text => text.includes('打卡') || text.includes('日期') || text.includes('星期'))) {
            hasAttendanceTable = true;
            break;
        }
    }
    
    if (hasAttendanceTable) {
        console.log('📊 检测到打卡表格，加班计算器插件已就绪');
        console.log('💡 使用方法：');
        console.log('  - 点击插件图标使用界面操作');
        console.log('  - 快捷键 Ctrl+Shift+O 快速计算');
        console.log('  - 快捷键 Ctrl+Shift+C 清除结果');
    }
});

// 在当前页面计算加班时间的函数
function calculateOvertimeInCurrentPage() {
    // 这里复用popup.js中的calculateOvertimeInPage函数逻辑
    // 为了避免重复代码，我们可以将核心逻辑提取到单独的文件中
    return window.calculateOvertimeInPage ? window.calculateOvertimeInPage() : null;
}

// 清除结果的函数
function clearOvertimeResults() {
    const existingResult = document.getElementById('overtime-extension-result');
    if (existingResult) {
        existingResult.remove();
        return true;
    }
    
    // 同时清除可能存在的控制台版本结果
    const consoleResult = document.getElementById('console-overtime-result');
    if (consoleResult) {
        consoleResult.remove();
    }
    
    return false;
}

// 添加页面右键菜单提示（通过CSS实现）
const style = document.createElement('style');
style.textContent = `
    /* 为插件结果添加动画效果 */
    #overtime-extension-result {
        animation: slideInFromRight 0.3s ease-out;
    }
    
    @keyframes slideInFromRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    /* 滚动条样式优化 */
    #overtime-extension-result::-webkit-scrollbar {
        width: 6px;
    }
    
    #overtime-extension-result::-webkit-scrollbar-track {
        background: rgba(255,255,255,0.1);
        border-radius: 3px;
    }
    
    #overtime-extension-result::-webkit-scrollbar-thumb {
        background: rgba(255,255,255,0.3);
        border-radius: 3px;
    }
    
    #overtime-extension-result::-webkit-scrollbar-thumb:hover {
        background: rgba(255,255,255,0.5);
    }
`;
document.head.appendChild(style);
