<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片压缩与裁剪工具</title>
    <!-- 引入Cropper.js的CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        .upload-container {
            border: 2px dashed #ccc;
            padding: 40px;
            margin: 20px 0;
            border-radius: 5px;
            cursor: pointer;
        }
        .upload-container:hover {
            border-color: #888;
        }
        #preview {
            max-width: 100%;
            margin: 20px 0;
            display: none;
        }
        .info {
            margin: 10px 0;
            font-size: 14px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .slider-container {
            margin: 20px 0;
        }
        #quality-value {
            display: inline-block;
            width: 40px;
        }
        .resize-container {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .resize-container h3 {
            margin-top: 0;
        }
        .resize-container input[type="number"] {
            width: 80px;
            margin: 0 10px;
            padding: 5px;
        }
        #reset-size {
            background-color: #607D8B;
            padding: 5px 10px;
            font-size: 14px;
        }
        /* 裁剪相关样式 */
        .crop-container {
            position: relative;
            margin: 20px 0;
            max-width: 100%;
            overflow: hidden;
            display: none;
        }

        /* 移除旧的裁剪区域样式 */
        #crop-area {
            display: none;
        }

        .resize-handle {
            display: none;
        }

        /* Cropper.js相关样式 */
        .cropper-container {
            margin: 0 auto;
        }

        .tool-buttons {
            margin: 10px 0;
        }

        #crop-btn {
            background-color: #2196F3;
        }

        #crop-btn:hover {
            background-color: #0b7dda;
        }

        #cancel-crop-btn {
            background-color: #f44336;
        }

        #cancel-crop-btn:hover {
            background-color: #d32f2f;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }

        .tab.active {
            background-color: white;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 新增尺寸调整控件样式 */
        .size-controls {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .size-controls input[type="number"] {
            width: 80px;
            margin: 0 10px;
            padding: 5px;
        }
    </style>
</head>
<body>
    <h1>图片压缩与裁剪工具</h1>

    <div class="upload-container" id="upload-area">
        <p>点击或拖拽图片到这里上传</p>
        <input type="file" id="file-input" accept="image/png, image/jpeg" style="display: none;">
    </div>

    <div class="tabs">
        <div class="tab active" data-tab="compress">压缩</div>
        <div class="tab" data-tab="crop">裁剪</div>
    </div>

    <div class="tab-content active" id="compress-tab">
        <div class="slider-container">
            <label for="quality">压缩质量: <span id="quality-value">80</span>%</label>
            <input type="range" id="quality" min="1" max="100" value="80">
        </div>

        <div class="resize-container">
            <h3>调整尺寸</h3>
            <div>
                <label for="width">宽度:</label>
                <input type="number" id="width" placeholder="宽度">
                <label for="height">高度:</label>
                <input type="number" id="height" placeholder="高度">
                <button id="reset-size">重置尺寸</button>
            </div>
            <div>
                <label>
                    <input type="checkbox" id="maintain-ratio" checked>
                    保持宽高比
                </label>
            </div>
        </div>

        <div class="info" id="original-info"></div>
        <div class="info" id="compressed-info"></div>

        <div>
            <button id="compress-btn" disabled>压缩图片</button>
            <button id="download-btn" disabled>下载压缩后的图片</button>
        </div>
    </div>

    <div class="tab-content" id="crop-tab">
        <div class="tool-buttons">
            <button id="start-crop-btn" disabled>开始裁剪</button>
            <button id="crop-btn" disabled>确认裁剪</button>
            <button id="cancel-crop-btn" disabled>取消裁剪</button>
            <button id="download-crop-btn" disabled>下载裁剪后的图片</button>
        </div>

        <!-- 添加裁剪形状选择 -->
        <div class="crop-shape-container" style="margin: 10px 0;">
            <label>裁剪形状:</label>
            <label style="margin: 0 10px;">
                <input type="radio" name="crop-shape" value="rectangle" checked> 矩形
            </label>
            <label>
                <input type="radio" name="crop-shape" value="circle"> 圆形
            </label>
        </div>

        <div class="crop-container" id="crop-container">
            <img id="crop-image" alt="裁剪预览">
        </div>

        <!-- 添加尺寸调整控件 -->
        <div class="size-controls">
            <h3>调整裁剪尺寸</h3>
            <div>
                <label for="crop-width">宽度:</label>
                <input type="number" id="crop-width" placeholder="宽度">
                <label for="crop-height">高度:</label>
                <input type="number" id="crop-height" placeholder="高度">
                <button id="crop-reset-size">重置尺寸</button>
            </div>
            <div style="margin-top: 10px;">
                <label>
                    <input type="checkbox" id="crop-maintain-ratio" checked>
                    保持宽高比
                </label>
            </div>
        </div>

        <div class="slider-container">
            <label for="crop-quality">裁剪后压缩质量: <span id="crop-quality-value">80</span>%</label>
            <input type="range" id="crop-quality" min="1" max="100" value="80">
        </div>

        <div class="info" id="crop-info"></div>
    </div>

    <img id="preview" alt="预览">

    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>
    <script>
        // 获取DOM元素
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        const preview = document.getElementById('preview');
        const originalInfo = document.getElementById('original-info');
        const compressedInfo = document.getElementById('compressed-info');
        const compressBtn = document.getElementById('compress-btn');
        const downloadBtn = document.getElementById('download-btn');
        const qualitySlider = document.getElementById('quality');
        const qualityValue = document.getElementById('quality-value');
        const widthInput = document.getElementById('width');
        const heightInput = document.getElementById('height');
        const resetSizeBtn = document.getElementById('reset-size');
        const maintainRatioCheckbox = document.getElementById('maintain-ratio');

        // 存储原始图片和压缩后的图片
        let originalImage = null;
        let compressedImage = null;

        let aspectRatio = 1;

        // 获取裁剪相关DOM元素
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        const startCropBtn = document.getElementById('start-crop-btn');
        const cropBtn = document.getElementById('crop-btn');
        const cancelCropBtn = document.getElementById('cancel-crop-btn');
        const downloadCropBtn = document.getElementById('download-crop-btn');
        const cropContainer = document.getElementById('crop-container');
        const cropImage = document.getElementById('crop-image');
        const cropQualitySlider = document.getElementById('crop-quality');
        const cropQualityValue = document.getElementById('crop-quality-value');
        const cropInfo = document.getElementById('crop-info');

        // 存储裁剪后的图片
        let croppedImage = null;
        let cropper = null;

        // 获取裁剪尺寸控制相关DOM元素
        const cropResizeContainer = document.getElementById('crop-resize-container');
        const cropWidthInput = document.getElementById('crop-width');
        const cropHeightInput = document.getElementById('crop-height');
        const cropResetSizeBtn = document.getElementById('crop-reset-size');
        const cropMaintainRatioCheckbox = document.getElementById('crop-maintain-ratio');

        let cropAspectRatio = 1;

        // 获取裁剪形状选择
        const cropShapeRadios = document.querySelectorAll('input[name="crop-shape"]');
        let isCircleCrop = false;

        // 监听裁剪形状选择变化
        cropShapeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                isCircleCrop = this.value === 'circle';

                if (cropper) {
                    // 如果是圆形，设置裁剪框为1:1比例
                    cropper.setAspectRatio(isCircleCrop ? 1 : NaN);

                    // 如果是圆形，确保保持宽高比选项被选中并禁用
                    if (isCircleCrop) {
                        cropMaintainRatioCheckbox.checked = true;
                        cropMaintainRatioCheckbox.disabled = true;
                    } else {
                        cropMaintainRatioCheckbox.disabled = false;
                    }
                }
            });
        });

        // 点击上传区域触发文件选择
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 处理拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#45a049';
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = '#ccc';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';

            if (e.dataTransfer.files.length) {
                handleFile(e.dataTransfer.files[0]);
            }
        });

        // 监听文件选择
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length) {
                handleFile(e.target.files[0]);
            }
        });

        // 监听质量滑块变化
        qualitySlider.addEventListener('input', () => {
            qualityValue.textContent = qualitySlider.value;
        });

        // 重置尺寸按钮点击事件
        resetSizeBtn.addEventListener('click', () => {
            if (originalImage) {
                widthInput.value = originalImage.width;
                heightInput.value = originalImage.height;
            }
        });

        // 保持宽高比
        widthInput.addEventListener('input', () => {
            if (maintainRatioCheckbox.checked && originalImage) {
                const newWidth = parseInt(widthInput.value) || 0;
                heightInput.value = Math.round(newWidth / aspectRatio);
            }
        });

        heightInput.addEventListener('input', () => {
            if (maintainRatioCheckbox.checked && originalImage) {
                const newHeight = parseInt(heightInput.value) || 0;
                widthInput.value = Math.round(newHeight * aspectRatio);
            }
        });

        // 压缩按钮点击事件
        compressBtn.addEventListener('click', compressImage);

        // 下载按钮点击事件
        downloadBtn.addEventListener('click', downloadImage);

        // 标签切换
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(tc => tc.classList.remove('active'));

                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });

        // 监听裁剪质量滑块变化
        cropQualitySlider.addEventListener('input', () => {
            cropQualityValue.textContent = cropQualitySlider.value;
        });

        // 开始裁剪按钮点击事件
        startCropBtn.addEventListener('click', () => {
            if (!originalImage) return;

            // 显示裁剪容器
            cropContainer.style.display = 'block';
            cropImage.src = originalImage.src;

            // 检查当前选择的裁剪形状
            isCircleCrop = document.querySelector('input[name="crop-shape"]:checked').value === 'circle';

            // 如果是圆形，确保保持宽高比选项被选中并禁用
            if (isCircleCrop) {
                cropMaintainRatioCheckbox.checked = true;
                cropMaintainRatioCheckbox.disabled = true;
            } else {
                cropMaintainRatioCheckbox.disabled = false;
            }

            // 初始化Cropper.js
            cropper = new Cropper(cropImage, {
                aspectRatio: isCircleCrop ? 1 : NaN, // 圆形必须是1:1比例
                viewMode: 1,      // 限制裁剪框不超出图片的范围
                guides: true,     // 显示裁剪框上的虚线
                center: true,     // 显示裁剪框中心的指示器
                highlight: false, // 显示裁剪区域的高亮
                background: true, // 显示容器的网格背景
                autoCropArea: 0.8, // 初始裁剪区域为图片的80%
                responsive: true,
                crop: function(event) {
                    // 实时更新裁剪尺寸
                    cropWidthInput.value = Math.round(event.detail.width);
                    cropHeightInput.value = Math.round(event.detail.height);
                    cropAspectRatio = event.detail.width / event.detail.height;
                }
            });

            // 显示裁剪尺寸控制区域
            cropResizeContainer.style.display = 'block';

            // 启用裁剪相关按钮
            cropBtn.disabled = false;
            cancelCropBtn.disabled = false;
            startCropBtn.disabled = true;
        });

        // 裁剪尺寸输入框事件
        cropWidthInput.addEventListener('input', () => {
            if (!cropper) return;

            const newWidth = parseInt(cropWidthInput.value) || 0;

            if (cropMaintainRatioCheckbox.checked) {
                // 保持宽高比
                cropHeightInput.value = Math.round(newWidth / cropAspectRatio);
            }

            // 应用新的裁剪框尺寸
            updateCropBoxSize();
        });

        cropHeightInput.addEventListener('input', () => {
            if (!cropper) return;

            const newHeight = parseInt(cropHeightInput.value) || 0;

            if (cropMaintainRatioCheckbox.checked) {
                // 保持宽高比
                cropWidthInput.value = Math.round(newHeight * cropAspectRatio);
            }

            // 应用新的裁剪框尺寸
            updateCropBoxSize();
        });

        // 重置裁剪尺寸按钮点击事件
        cropResetSizeBtn.addEventListener('click', () => {
            if (!cropper) return;

            // 重置裁剪框为默认大小
            cropper.reset();
        });

        // 更新裁剪框尺寸
        function updateCropBoxSize() {
            if (!cropper) return;

            const width = parseInt(cropWidthInput.value) || 0;
            const height = parseInt(cropHeightInput.value) || 0;

            if (width <= 0 || height <= 0) return;

            // 获取当前裁剪框数据
            const cropBoxData = cropper.getCropBoxData();

            // 设置新的裁剪框尺寸
            cropper.setCropBoxData({
                ...cropBoxData,
                width: width,
                height: height
            });
        }

        // 确认裁剪按钮点击事件
        cropBtn.addEventListener('click', () => {
            if (!originalImage || !cropper) return;

            // 获取裁剪后的Canvas
            const canvas = cropper.getCroppedCanvas({
                width: parseInt(cropWidthInput.value) || undefined,
                height: parseInt(cropHeightInput.value) || undefined,
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high'
            });

            if (!canvas) {
                alert('裁剪失败！');
                return;
            }

            // 如果是圆形裁剪，创建一个新的圆形Canvas
            let finalCanvas = canvas;
            if (isCircleCrop) {
                const circleCanvas = document.createElement('canvas');
                const ctx = circleCanvas.getContext('2d');

                // 设置圆形Canvas的尺寸
                const size = Math.min(canvas.width, canvas.height);
                circleCanvas.width = size;
                circleCanvas.height = size;

                // 创建圆形裁剪路径
                ctx.beginPath();
                ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
                ctx.closePath();
                ctx.clip();

                // 绘制裁剪后的图像到圆形Canvas
                ctx.drawImage(canvas, 0, 0, size, size);

                finalCanvas = circleCanvas;
            }

            // 获取压缩质量
            const quality = parseInt(cropQualitySlider.value) / 100;

            // 根据原始图片类型选择输出格式
            const outputType = originalImage.type;

            // 压缩裁剪后的图片
            const croppedDataUrl = finalCanvas.toDataURL(outputType, quality);

            // 计算裁剪后的大小
            const croppedSize = Math.round((croppedDataUrl.length - croppedDataUrl.indexOf(',') - 1) * 0.75);

            // 更新裁剪信息
            cropInfo.innerHTML = `裁剪后: ${finalCanvas.width} x ${finalCanvas.height} 像素, ${formatFileSize(croppedSize)}`;

            // 存储裁剪后的图片
            croppedImage = {
                dataUrl: croppedDataUrl,
                type: outputType,
                size: croppedSize,
                width: finalCanvas.width,
                height: finalCanvas.height
            };

            // 更新预览
            preview.src = croppedDataUrl;
            preview.style.display = 'block';

            // 如果是圆形裁剪，添加圆形样式到预览图
            if (isCircleCrop) {
                preview.style.borderRadius = '50%';
            } else {
                preview.style.borderRadius = '0';
            }

            // 启用下载按钮
            downloadCropBtn.disabled = false;

            // 销毁裁剪器并隐藏裁剪容器
            cropper.destroy();
            cropper = null;
            cropContainer.style.display = 'none';
            cropResizeContainer.style.display = 'none';
            startCropBtn.disabled = false;
        });

        // 取消裁剪按钮点击事件
        cancelCropBtn.addEventListener('click', () => {
            if (cropper) {
                cropper.destroy();
                cropper = null;
            }
            cropContainer.style.display = 'none';
            cropResizeContainer.style.display = 'none';
            startCropBtn.disabled = false;
            cropBtn.disabled = true;
            cancelCropBtn.disabled = true;
        });

        // 下载裁剪后的图片
        downloadCropBtn.addEventListener('click', () => {
            if (!croppedImage) return;

            // 创建下载链接
            const link = document.createElement('a');
            link.href = croppedImage.dataUrl;

            // 设置文件名
            const extension = croppedImage.type.split('/')[1];
            const shapeText = isCircleCrop ? 'circle_' : '';
            link.download = `${shapeText}cropped_image.${extension}`;

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });

        // 处理上传的文件
        function handleFile(file) {
            if (!file.type.match('image/jpeg') && !file.type.match('image/png')) {
                alert('请上传JPG或PNG格式的图片！');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                // 创建图片对象
                const img = new Image();
                img.onload = function() {
                    // 显示原始图片信息
                    originalImage = {
                        element: img,
                        src: e.target.result,
                        width: img.width,
                        height: img.height,
                        size: file.size,
                        type: file.type
                    };

                    // 计算宽高比
                    aspectRatio = img.width / img.height;

                    // 设置尺寸输入框的初始值
                    widthInput.value = img.width;
                    heightInput.value = img.height;

                    // 显示预览
                    preview.src = e.target.result;
                    preview.style.display = 'block';

                    // 显示原始图片信息
                    originalInfo.innerHTML = `原始图片: ${img.width} x ${img.height} 像素, ${formatFileSize(file.size)}`;

                    // 启用压缩按钮
                    compressBtn.disabled = false;

                    // 重置压缩信息
                    compressedInfo.innerHTML = '';
                    downloadBtn.disabled = true;

                    // 启用裁剪按钮
                    startCropBtn.disabled = false;
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        // 压缩图片
        function compressImage() {
            if (!originalImage) return;

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // 获取用户输入的尺寸
            const newWidth = parseInt(widthInput.value) || originalImage.width;
            const newHeight = parseInt(heightInput.value) || originalImage.height;

            // 设置画布尺寸为用户指定的尺寸
            canvas.width = newWidth;
            canvas.height = newHeight;

            // 绘制调整大小后的图片到画布
            ctx.drawImage(originalImage.element, 0, 0, newWidth, newHeight);

            // 获取压缩质量
            const quality = parseInt(qualitySlider.value) / 100;

            // 根据原始图片类型选择输出格式
            const outputType = originalImage.type;

            // 压缩图片
            const compressedDataUrl = canvas.toDataURL(outputType, quality);

            // 计算压缩后的大小
            const compressedSize = Math.round((compressedDataUrl.length - compressedDataUrl.indexOf(',') - 1) * 0.75);

            // 更新压缩信息
            compressedInfo.innerHTML = `压缩后: ${newWidth} x ${newHeight} 像素, ${formatFileSize(compressedSize)}, 压缩率: ${Math.round((1 - compressedSize / originalImage.size) * 100)}%`;

            // 存储压缩后的图片
            compressedImage = {
                dataUrl: compressedDataUrl,
                type: outputType,
                size: compressedSize
            };

            // 更新预览
            preview.src = compressedDataUrl;

            // 启用下载按钮
            downloadBtn.disabled = false;
        }

        // 下载压缩后的图片
        function downloadImage() {
            if (!compressedImage) return;

            // 创建下载链接
            const link = document.createElement('a');
            link.href = compressedImage.dataUrl;

            // 设置文件名
            const extension = compressedImage.type.split('/')[1];
            link.download = `compressed_image.${extension}`;

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) {
                return bytes + ' B';
            } else if (bytes < 1024 * 1024) {
                return (bytes / 1024).toFixed(2) + ' KB';
            } else {
                return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
            }
        }
    </script>
</body>
</html>
