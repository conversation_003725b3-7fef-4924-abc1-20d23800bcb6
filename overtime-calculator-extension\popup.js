document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculateBtn');
    const clearBtn = document.getElementById('clearBtn');
    const status = document.getElementById('status');
    
    // 显示状态信息
    function showStatus(message, type = 'info') {
        status.textContent = message;
        status.className = `status ${type}`;
        status.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            status.style.display = 'none';
        }, 3000);
    }
    
    // 计算加班时间
    calculateBtn.addEventListener('click', async function() {
        try {
            showStatus('正在计算加班时间...', 'info');
            
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // 注入计算脚本
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: calculateOvertimeInPage
            });
            
            const result = results[0].result;
            
            if (result.success) {
                showStatus(`✅ 计算完成！总加班: ${result.totalHours}小时${result.totalMinutes}分钟`, 'success');
            } else {
                showStatus(`❌ ${result.error}`, 'error');
            }
            
        } catch (error) {
            console.error('执行脚本时出错:', error);
            showStatus('❌ 执行失败，请刷新页面后重试', 'error');
        }
    });
    
    // 清除结果
    clearBtn.addEventListener('click', async function() {
        try {
            showStatus('正在清除结果...', 'info');
            
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: clearOvertimeResults
            });
            
            showStatus('🗑️ 结果已清除', 'success');
            
        } catch (error) {
            console.error('清除结果时出错:', error);
            showStatus('❌ 清除失败', 'error');
        }
    });
});

// 在页面中执行的计算函数
function calculateOvertimeInPage() {
    // 加班计算核心函数
    function calculateOvertime(attendanceData) {
        const results = [];
        
        attendanceData.forEach(record => {
            const overtimeInfo = calculateDailyOvertime(record);
            results.push({
                ...record,
                overtimeHours: overtimeInfo.hours,
                overtimeMinutes: overtimeInfo.minutes,
                overtimeReason: overtimeInfo.reason
            });
        });
        
        return results;
    }
    
    function calculateDailyOvertime(record) {
        const { date, dayOfWeek, punchTimes } = record;
        
        const validPunchTimes = punchTimes.filter(time => time && time.trim() !== '');
        
        if (validPunchTimes.length === 0) {
            return { hours: 0, minutes: 0, reason: '无打卡记录' };
        }
        
        const isWeekend = dayOfWeek === '六' || dayOfWeek === '日';
        
        if (isWeekend) {
            return calculateWeekendOvertime(validPunchTimes);
        } else {
            return calculateWeekdayOvertime(validPunchTimes);
        }
    }
    
    function calculateWeekdayOvertime(punchTimes) {
        const firstPunch = parseTime(punchTimes[0]);
        if (firstPunch.hours >= 12) {
            return { hours: 0, minutes: 0, reason: '第一次打卡时间大于12点' };
        }
        
        if (punchTimes.length === 1) {
            if (firstPunch.hours < 18 || (firstPunch.hours === 18 && firstPunch.minutes < 30)) {
                return { hours: 0, minutes: 0, reason: '无下班卡且18点30分前无打卡记录' };
            }
        }

        const lastPunch = parseTime(punchTimes[punchTimes.length - 1]);

        if (lastPunch.hours < 18 || (lastPunch.hours === 18 && lastPunch.minutes < 30)) {
            return { hours: 0, minutes: 0, reason: '下班卡未超过18点30分' };
        }

        const overtimeStartTime = { hours: 18, minutes: 30 };
        const overtimeMinutes = calculateTimeDifference(overtimeStartTime, lastPunch);
        
        const flexMinutes = calculateFlexTime(firstPunch);
        const finalOvertimeMinutes = Math.max(0, overtimeMinutes - flexMinutes);
        
        return {
            hours: Math.floor(finalOvertimeMinutes / 60),
            minutes: finalOvertimeMinutes % 60,
            reason: finalOvertimeMinutes > 0 ? '工作日加班' : '无加班'
        };
    }
    
    function calculateWeekendOvertime(punchTimes) {
        if (punchTimes.length < 2) {
            return { hours: 0, minutes: 0, reason: '周末打卡记录不完整' };
        }
        
        const sortedTimes = punchTimes.map(parseTime).sort((a, b) => {
            return (a.hours * 60 + a.minutes) - (b.hours * 60 + b.minutes);
        });
        
        const firstPunch = sortedTimes[0];
        const lastPunch = sortedTimes[sortedTimes.length - 1];
        
        let totalMinutes = calculateTimeDifference(firstPunch, lastPunch);
        
        if (firstPunch.hours < 12 && lastPunch.hours >= 13) {
            totalMinutes -= 60;
        } else if (firstPunch.hours < 13 && lastPunch.hours >= 13 && 
                   (firstPunch.hours > 12 || (firstPunch.hours === 12 && firstPunch.minutes > 0))) {
            const lunchStartMinutes = Math.max(0, 60 - firstPunch.minutes);
            totalMinutes -= lunchStartMinutes;
        }
        
        return {
            hours: Math.floor(totalMinutes / 60),
            minutes: totalMinutes % 60,
            reason: totalMinutes > 0 ? '周末加班' : '无加班'
        };
    }
    
    function parseTime(timeStr) {
        const parts = timeStr.split(':');
        return {
            hours: parseInt(parts[0], 10),
            minutes: parseInt(parts[1], 10),
            seconds: parseInt(parts[2], 10)
        };
    }
    
    function calculateFlexTime(firstPunch) {
        const standardStartHour = 8;
        const standardStartMinute = 30;
        
        if (firstPunch.hours < standardStartHour || 
            (firstPunch.hours === standardStartHour && firstPunch.minutes <= standardStartMinute)) {
            return 0;
        }
        
        return (firstPunch.hours - standardStartHour) * 60 + 
               (firstPunch.minutes - standardStartMinute);
    }
    
    function calculateTimeDifference(startTime, endTime) {
        const startMinutes = startTime.hours * 60 + startTime.minutes;
        const endMinutes = endTime.hours * 60 + endTime.minutes;
        return Math.max(0, endMinutes - startMinutes);
    }
    
    // 从表格提取数据的函数
    function extractAttendanceDataFromTable() {
        const tables = document.querySelectorAll('table');
        let targetTable = null;
        
        // 寻找包含打卡数据的表格
        for (let table of tables) {
            const headers = table.querySelectorAll('th');
            const headerTexts = Array.from(headers).map(th => th.textContent.trim());
            
            // 检查是否包含打卡相关的列头
            if (headerTexts.some(text => text.includes('打卡') || text.includes('日期') || text.includes('星期'))) {
                targetTable = table;
                break;
            }
        }
        
        if (!targetTable) {
            throw new Error('未找到包含打卡数据的表格');
        }
        
        const rows = targetTable.querySelectorAll('tbody tr');
        const data = [];
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 3) {
                const punchTimes = [];
                
                // 从第4列开始提取时间数据（跳过序号、日期、星期）
                for (let i = 3; i < cells.length; i++) {
                    const timeText = cells[i].textContent.trim();
                    if (timeText && timeText.match(/^\d{2}:\d{2}:\d{2}$/)) {
                        punchTimes.push(timeText);
                    }
                }
                
                if (cells.length >= 3) {
                    data.push({
                        序号: cells[0].textContent.trim(),
                        date: cells[1].textContent.trim(),
                        dayOfWeek: cells[2].textContent.trim(),
                        punchTimes: punchTimes
                    });
                }
            }
        });
        
        return data;
    }
    
    try {
        const attendanceData = extractAttendanceDataFromTable();
        const overtimeResults = calculateOvertime(attendanceData);
        
        // 移除之前的结果
        const existingResult = document.getElementById('overtime-extension-result');
        if (existingResult) {
            existingResult.remove();
        }
        
        // 创建结果显示
        const resultContainer = document.createElement('div');
        resultContainer.id = 'overtime-extension-result';
        resultContainer.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 400px;
            max-height: 500px;
            overflow-y: auto;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            color: white;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            z-index: 10000;
            padding: 20px;
        `;
        
        // 计算总加班时间
        const totalOvertimeMinutes = overtimeResults.reduce((total, record) => {
            return total + record.overtimeHours * 60 + record.overtimeMinutes;
        }, 0);
        
        const totalHours = Math.floor(totalOvertimeMinutes / 60);
        const totalMinutes = totalOvertimeMinutes % 60;
        
        // 生成HTML内容
        let detailsHTML = '';
        let hasOvertime = false;
        
        overtimeResults.forEach(record => {
            if (record.overtimeHours > 0 || record.overtimeMinutes > 0) {
                hasOvertime = true;
                detailsHTML += `
                    <div style="margin-bottom: 8px; padding: 8px; background: rgba(255,255,255,0.15); border-radius: 6px; border-left: 4px solid #ffd700;">
                        <strong>${record.date} (${record.dayOfWeek}):</strong> 
                        <span style="color: #ffd700; font-weight: bold;">${record.overtimeHours}小时${record.overtimeMinutes}分钟</span>
                        <span style="opacity: 0.8; font-size: 12px;"> - ${record.overtimeReason}</span>
                    </div>
                `;
            }
        });
        
        if (!hasOvertime) {
            detailsHTML = '<div style="text-align: center; padding: 20px; opacity: 0.8;">😴 本期间无加班记录</div>';
        }
        
        resultContainer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0; font-size: 18px;">⏰ 加班统计</h3>
                <button onclick="this.parentElement.parentElement.remove()" style="background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-size: 14px;">×</button>
            </div>
            <div style="margin-bottom: 15px; max-height: 300px; overflow-y: auto;">
                ${detailsHTML}
            </div>
            <div style="text-align: center; font-size: 16px; font-weight: bold; padding: 12px; background: rgba(255,255,255,0.2); border-radius: 8px;">
                🎯 总加班时间: ${totalHours}小时${totalMinutes}分钟
            </div>
            <div style="text-align: center; margin-top: 10px; font-size: 11px; opacity: 0.7;">
                由加班计算器插件生成
            </div>
        `;
        
        document.body.appendChild(resultContainer);
        
        return {
            success: true,
            totalHours,
            totalMinutes,
            details: overtimeResults.filter(r => r.overtimeHours > 0 || r.overtimeMinutes > 0)
        };
        
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// 清除结果的函数
function clearOvertimeResults() {
    const existingResult = document.getElementById('overtime-extension-result');
    if (existingResult) {
        existingResult.remove();
        return true;
    }
    return false;
}
